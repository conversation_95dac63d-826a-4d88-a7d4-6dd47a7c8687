/**
 * 外卖食品服务
 *
 * 本文件实现了外卖食品的业务逻辑层，处理外卖商品的创建、查询、更新等业务操作。
 * 作为仓库层和控制器层之间的桥梁，封装复杂的业务逻辑。
 */

package services

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"o_mall_backend/common/result"
	merchantModels "o_mall_backend/modules/merchant/models"
	merchantRepositories "o_mall_backend/modules/merchant/repositories"
	"o_mall_backend/modules/takeout/dto"
	"o_mall_backend/modules/takeout/models"
	"o_mall_backend/modules/takeout/repositories"

	"github.com/beego/beego/v2/core/logs"
)

// TakeoutFoodService 外卖食品服务接口
type TakeoutFoodService interface {
	// 基础CRUD操作
	CreateFood(req *dto.CreateTakeoutFoodRequest) (int64, error)
	GetFoodByID(id int64) (*dto.TakeoutFoodDetailDTO, error)
	UpdateFood(req *dto.UpdateTakeoutFoodRequest) error
	DeleteFood(id int64) error

	// 列表查询
	ListFoods(query *dto.TakeoutFoodQueryRequest) (*dto.TakeoutFoodResponseList, error)

	// 商家和食品列表查询
	ListMerchants(keyword string, page, pageSize int) ([]dto.MerchantListItemDTO, int64, error)
	GetMerchantDetail(merchantID int64) (*dto.MerchantDetailDTO, error)
	GetFoodsByMerchantID(merchantID int64, categoryID int64, keyword string, page, pageSize int) (*dto.TakeoutFoodResponseList, error)

	// 业务操作
	UpdateFoodStatus(id int64, status int) error
	UpdateSoldOutStatus(id int64, soldOut bool) error
	IncrementViewCount(id int64) error

	// 获取价格区间
	GetFoodPriceRange(foodID int64) (float64, float64, error)

	// 管理员专用操作
	GetFoodsPageForAdmin(merchantID int64, categoryID int64, keyword string, status int, auditStatus int, page, pageSize int) ([]dto.AdminFoodListItemDTO, int64, error)
	GetFoodDetailForAdmin(id int64) (*dto.AdminFoodDetailDTO, error)
	CreateFoodForAdmin(req *dto.CreateFoodRequest) (int64, error)
	UpdateFoodForAdmin(req *dto.UpdateFoodRequest) error
	UpdateFoodStatusForAdmin(req *dto.UpdateFoodStatusRequest) error
	AuditFoodForAdmin(req *dto.AuditFoodRequest) error
	DeleteFoodForAdmin(id int64) error
	ValidateFoodOwnership(foodID int64, merchantID int64) (bool, error)

	// 商家统计
	GetFoodStatisticsByMerchantID(merchantID int64) (totalCount, onSaleCount, pendingCount, soldOutCount int, err error)

	// 缓存管理
	ClearMerchantFoodCache(merchantID int64) error

	// 获取用户在指定商家的促销和优惠券信息
	GetMerchantsPromotionsAndCoupons(userID int64, merchantIDs []int64) ([]dto.MerchantPromotionCouponInfo, error)

	// 获取商家可领取的优惠券和促销信息
	GetMerchantAvailableCoupons(merchantID int64) (*dto.MerchantAvailableCouponsInfo, error)
}

// takeoutFoodService 外卖食品服务实现
type takeoutFoodService struct {
	foodRepo         repositories.TakeoutFoodRepository
	variantRepo      repositories.TakeoutVariantRepository
	comboRepo        repositories.TakeoutComboRepository
	categoryRepo     repositories.TakeoutCategoryRepository
	merchantRepo     merchantRepositories.MerchantRepository
	cacheService     TakeoutFoodCacheService
	promotionService ITakeoutPromotionService // 添加促销服务依赖
}

// NewTakeoutFoodService 创建外卖食品服务实例
func NewTakeoutFoodService() TakeoutFoodService {
	// 创建促销服务实例
	promotionRepo := repositories.NewTakeoutPromotionRepository()
	foodPromoRepo := repositories.NewTakeoutFoodPromotionRepository()
	couponRepo := repositories.NewTakeoutCouponRepository()
	userCouponRepo := repositories.NewTakeoutUserCouponRepository()
	userPromotionRepo := repositories.NewTakeoutUserPromotionRepository()
	foodRepo := repositories.NewTakeoutFoodRepository()

	promotionService := NewTakeoutPromotionService(
		promotionRepo,
		foodPromoRepo,
		foodRepo,
		couponRepo,
		userCouponRepo,
		userPromotionRepo,
	)

	return &takeoutFoodService{
		foodRepo:         foodRepo,
		variantRepo:      repositories.NewTakeoutVariantRepository(),
		comboRepo:        repositories.NewTakeoutComboRepository(),
		categoryRepo:     repositories.NewTakeoutCategoryRepository(),
		merchantRepo:     merchantRepositories.NewMerchantRepository(),
		cacheService:     NewTakeoutFoodCacheService(),
		promotionService: promotionService,
	}
}

// CreateFood 创建外卖食品
func (s *takeoutFoodService) CreateFood(req *dto.CreateTakeoutFoodRequest) (int64, error) {
	// 检查分类是否存在
	_, err := s.categoryRepo.GetByID(req.CategoryID)
	if err != nil {
		return 0, err
	}

	// 创建食品模型
	food := &models.TakeoutFood{
		MerchantID:      req.MerchantID,
		CategoryID:      req.CategoryID,
		Name:            req.Name,
		Description:     req.Description,
		Brief:           req.Brief,
		Image:           req.Image,
		Price:           req.Price,
		OriginalPrice:   req.OriginalPrice,
		PackagingFee:    req.PackagingFee,
		PreparationTime: req.PreparationTime,
		IsCombination:   req.IsCombination,
		IsSpicy:         req.IsSpicy,
		HasVariants:     req.HasVariants,
		DailyLimit:      req.DailyLimit,
		Tags:            strings.Join(req.Tags, ","),
		Keywords:        req.Keywords,
		Status:          req.Status,
		IsRecommend:     req.IsRecommend,
		SortOrder:       req.SortOrder,
		IsVisible:       true,
	}

	// 如果没有设置原价，则使用当前价格
	if food.OriginalPrice <= 0 {
		food.OriginalPrice = food.Price
	}

	// 如果没有设置备餐时间，则默认为15分钟
	if food.PreparationTime <= 0 {
		food.PreparationTime = 15
	}

	// 保存到数据库
	id, err := s.foodRepo.Create(food)
	if err != nil {
		logs.Error("创建外卖食品失败: %v, 请求: %+v", err, req)
		return 0, errors.New("创建食品失败")
	}

	// 异步更新缓存
	go func() {
		// 清理相关缓存
		s.cacheService.ClearMerchantFoodCache(req.MerchantID)
		if req.CategoryID > 0 {
			s.cacheService.ClearCategoryFoodCache(req.CategoryID)
		}
		logs.Debug("食品创建后缓存清理完成, 食品ID: %d", id)
	}()

	return id, nil
}

// GetFoodByID 根据ID获取外卖食品详情
func (s *takeoutFoodService) GetFoodByID(id int64) (*dto.TakeoutFoodDetailDTO, error) {
	// 尝试从缓存获取食品详情
	cachedDetail, err := s.cacheService.GetFoodDetail(id)
	if err == nil && cachedDetail != nil {
		// 缓存命中，异步增加浏览次数
		go s.foodRepo.IncrementViewCount(id)
		return cachedDetail, nil
	}

	// 缓存未命中，从数据库获取食品基本信息
	food, err := s.foodRepo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// 增加浏览次数
	go s.foodRepo.IncrementViewCount(id)

	// 获取分类信息
	category, err := s.categoryRepo.GetByID(food.CategoryID)
	if err != nil {
		logs.Warn("获取分类信息失败: %v, 食品ID: %d", err, id)
		// 不影响食品信息返回
	}

	categoryName := ""
	if category != nil {
		categoryName = category.Name
	}

	// 构造响应DTO
	detailDTO := &dto.TakeoutFoodDetailDTO{
		ID:               food.ID,
		MerchantID:       food.MerchantID,
		Name:             food.Name,
		Description:      food.Description,
		Brief:            food.Brief,
		Image:            food.Image,
		Price:            food.Price,
		OriginalPrice:    food.OriginalPrice,
		PackagingFee:     food.PackagingFee,
		PreparationTime:  food.PreparationTime,
		IsCombination:    food.IsCombination,
		IsSpicy:          food.IsSpicy,
		HasVariants:      food.HasVariants,
		SoldOut:          food.SoldOut,
		DailyLimit:       food.DailyLimit,
		TotalSold:        food.TotalSold,
		CategoryID:       food.CategoryID,
		CategoryName:     categoryName,
		GlobalCategoryID: food.GlobalCategoryID, // 设置全局分类ID
		Status:           food.Status,
		AuditStatus:      food.AuditStatus,
		AuditReason:      food.AuditReason,
		AuditTime:        food.AuditTime,
		CreatedAt:        food.CreatedAt,
		UpdatedAt:        food.UpdatedAt,
	}

	// 转换标签
	if food.Tags != "" {
		detailDTO.Tags = strings.Split(food.Tags, ",")
	}

	// 转换关键词
	if food.Keywords != "" {
		detailDTO.Keywords = strings.Split(food.Keywords, ",")
	}

	// 获取变体信息（如果有）
	if food.HasVariants {
		variants, err := s.variantRepo.ListByFoodID(id)
		if err != nil {
			logs.Warn("获取规格变体失败: %v, 食品ID: %d", err, id)
		} else {
			variantDTOs := make([]dto.TakeoutFoodVariantDTO, 0, len(variants))
			for _, v := range variants {
				variantDTOs = append(variantDTOs, dto.TakeoutFoodVariantDTO{
					ID:            v.ID,
					FoodID:        v.FoodID,
					Name:          v.Name,
					Description:   v.Description,
					Image:         v.Image,
					Price:         v.Price,
					OriginalPrice: v.OriginalPrice,
					Stock:         v.Stock,
					SoldCount:     v.SoldCount,
					IsDefault:     v.IsDefault,
					SortOrder:     v.SortOrder,
				})
			}
			detailDTO.Variants = variantDTOs
		}

		// 计算价格区间
		minPrice, maxPrice, err := s.GetFoodPriceRange(id)
		if err == nil {
			detailDTO.MinPrice = minPrice
			detailDTO.MaxPrice = maxPrice
		} else {
			detailDTO.MinPrice = food.Price
			detailDTO.MaxPrice = food.Price
		}
	} else {
		detailDTO.MinPrice = food.Price
		detailDTO.MaxPrice = food.Price
	}

	// 获取套餐组件信息（如果是套餐组合）
	if food.IsCombination {
		comboItems, err := s.comboRepo.ListComboItemsByFoodID(id)
		if err != nil {
			logs.Warn("获取套餐组件失败: %v, 食品ID: %d", err, id)
		} else {
			comboItemDTOs := make([]dto.TakeoutComboItemDTO, 0, len(comboItems))
			for _, item := range comboItems {
				// 获取套餐选项
				options, err := s.comboRepo.ListComboOptionsByItemID(item.ID)
				if err != nil {
					logs.Warn("获取套餐选项失败: %v, 组件ID: %d", err, item.ID)
					continue
				}

				optionDTOs := make([]dto.TakeoutComboOptionDTO, 0, len(options))
				for _, opt := range options {
					optionDTOs = append(optionDTOs, dto.TakeoutComboOptionDTO{
						ID:           opt.ID,
						ComboItemID:  opt.ComboItemID,
						Name:         opt.Name,
						Description:  opt.Description,
						Image:        opt.Image,
						ExtraPrice:   opt.ExtraPrice,
						Stock:        opt.Stock,
						SoldCount:    opt.SoldCount,
						IsDefault:    opt.IsDefault,
						MaxPerOrder:  opt.MaxPerOrder,
						IsIndividual: opt.IsIndividual,
						SortOrder:    opt.SortOrder,
					})
				}

				comboItemDTOs = append(comboItemDTOs, dto.TakeoutComboItemDTO{
					ID:          item.ID,
					FoodID:      item.FoodID,
					Name:        item.Name,
					Description: item.Description,
					MinSelect:   item.MinSelect,
					MaxSelect:   item.MaxSelect,
					IsRequired:  item.IsRequired,
					SortOrder:   item.SortOrder,
					Options:     optionDTOs,
				})
			}
			// 直接使用takeout_combo_dto.go中定义的TakeoutComboItemDTO
			detailDTO.ComboItems = comboItemDTOs
		}
	}

	// 异步设置缓存
	go func() {
		err := s.cacheService.SetFoodDetail(id, detailDTO)
		if err != nil {
			logs.Warn("设置食品详情缓存失败: %v, 食品ID: %d", err, id)
		}
	}()

	return detailDTO, nil
}

// UpdateFood 更新外卖食品
func (s *takeoutFoodService) UpdateFood(req *dto.UpdateTakeoutFoodRequest) error {
	// 获取现有食品信息
	food, err := s.foodRepo.GetByID(req.ID)
	if err != nil {
		return err
	}

	// 如果分类ID有变更，检查新分类是否存在
	if req.CategoryID != nil && *req.CategoryID > 0 && *req.CategoryID != food.CategoryID {
		_, err = s.categoryRepo.GetByID(*req.CategoryID)
		if err != nil {
			return err
		}
		food.CategoryID = *req.CategoryID
	}

	// 更新全局分类ID
	if req.GlobalCategoryID != nil {
		// 如果设置为0，表示清空全局分类
		food.GlobalCategoryID = *req.GlobalCategoryID
	}

	// 更新字段
	if req.Name != "" {
		food.Name = req.Name
	}
	if req.Description != "" {
		food.Description = req.Description
	}
	if req.Brief != "" {
		food.Brief = req.Brief
	}
	if req.Image != "" {
		food.Image = req.Image
	}
	if req.Price != nil && *req.Price > 0 {
		food.Price = *req.Price
	}
	if req.OriginalPrice != nil && *req.OriginalPrice > 0 {
		food.OriginalPrice = *req.OriginalPrice
	}
	if req.PackagingFee != nil {
		food.PackagingFee = *req.PackagingFee
	}
	if req.PreparationTime != nil && *req.PreparationTime > 0 {
		food.PreparationTime = *req.PreparationTime
	}
	if len(req.Tags) > 0 {
		food.Tags = strings.Join(req.Tags, ",")
	}
	if len(req.Keywords) > 0 {
		food.Keywords = strings.Join(req.Keywords, ",")
	}
	if req.DailyLimit != nil {
		food.DailyLimit = *req.DailyLimit
	}

	// 状态更新安全限制
	if req.Status != nil {
		// 添加调试日志
		logs.Info("[UpdateFood] 请求更新状态: %d, 当前状态: %d", *req.Status, food.Status)

		// 商户只能设置以下状态：草稿(0)、上架销售中(1)、已下架(2)、已售罄(3)
		// 根据模型定义的常量进行映射
		allowedStatuses := map[int]bool{
			models.FoodStatusDraft:   true, // 0-草稿状态
			models.FoodStatusOnSale:  true, // 1-上架销售中
			models.FoodStatusOffSale: true, // 2-已下架
			models.FoodStatusSoldOut: true, // 3-已售罄
		}

		// 验证是否为允许商户设置的状态
		if allowedStatuses[*req.Status] {
			logs.Info("[UpdateFood] 状态验证通过，设置新状态: %d", *req.Status)

			// 如果当前审核状态是已通过(1)，需要进行特殊处理
			if food.AuditStatus == models.AuditStatusApproved {
				// 可以设置销售状态，但不能更改审核状态
				// 如果用户尝试将商品重新提交审核，需要明确提示
				if req.AuditStatus != nil && *req.AuditStatus == models.AuditStatusPending {
					return errors.New("已审核通过的商品不能重新提交审核，请联系管理员")
				}
			}

			// 如果目标审核状态是待审核(0)，检查商品信息是否完整
			if req.AuditStatus != nil && *req.AuditStatus == models.AuditStatusPending {
				// TODO: 可以在这里添加更多验证，确保提交审核的商品信息完整
				food.AuditStatus = models.AuditStatusPending
			}

			food.Status = *req.Status
			logs.Info("[UpdateFood] 状态更新成功，新状态: %d", food.Status)
		} else {
			logs.Error("[UpdateFood] 状态验证失败，请求状态: %d, 允许的状态: %v", *req.Status, allowedStatuses)
			return errors.New("无权设置所请求的商品状态，该操作需要管理员权限")
		}
	}

	if req.SoldOut != nil {
		food.SoldOut = *req.SoldOut
		// 如果设置为售罄，同时更新状态
		if *req.SoldOut && food.Status == models.FoodStatusOnSale {
			food.Status = models.FoodStatusSoldOut
		}
	}

	if req.IsRecommend != nil {
		food.IsRecommend = *req.IsRecommend
	}
	if req.SortOrder != nil {
		food.SortOrder = *req.SortOrder
	}

	// 保存到数据库
	err = s.foodRepo.Update(food)
	if err != nil {
		logs.Error("更新外卖食品失败: %v, 请求: %+v", err, req)
		return errors.New("更新食品失败")
	}

	// 异步更新缓存
	go func() {
		// 删除单个食品缓存
		s.cacheService.DeleteFoodCache(req.ID)
		// 清理商家相关缓存
		s.cacheService.ClearMerchantFoodCache(food.MerchantID)
		// 如果分类有变更，清理相关分类缓存
		if req.CategoryID != nil && *req.CategoryID > 0 {
			s.cacheService.ClearCategoryFoodCache(*req.CategoryID)
		}
		s.cacheService.ClearCategoryFoodCache(food.CategoryID)
		logs.Debug("食品更新后缓存清理完成, 食品ID: %d", req.ID)
	}()

	return nil
}

// DeleteFood 删除外卖食品
func (s *takeoutFoodService) DeleteFood(id int64) error {
	// TODO: 检查该食品是否已被订单引用

	// 获取食品信息用于缓存清理
	food, err := s.foodRepo.GetByID(id)
	if err != nil {
		logs.Error("获取待删除食品信息失败: %v, ID: %d", err, id)
		return errors.New("获取食品信息失败")
	}

	// 删除食品
	err = s.foodRepo.Delete(id)
	if err != nil {
		logs.Error("删除外卖食品失败: %v, ID: %d", err, id)
		return errors.New("删除食品失败")
	}

	// 异步清理缓存
	go func() {
		// 删除单个食品缓存
		s.cacheService.DeleteFoodCache(id)
		// 清理商家相关缓存
		s.cacheService.ClearMerchantFoodCache(food.MerchantID)
		// 清理分类相关缓存
		if food.CategoryID > 0 {
			s.cacheService.ClearCategoryFoodCache(food.CategoryID)
		}
		logs.Debug("食品删除后缓存清理完成, 食品ID: %d", id)
	}()

	return nil
}

// GetFoodsByMerchantID 获取指定商家的食品及规格列表
// 返回的每个食品包含Variants字段
func (s *takeoutFoodService) GetFoodsByMerchantID(merchantID int64, categoryID int64, keyword string, page, pageSize int) (*dto.TakeoutFoodResponseList, error) {
	// 尝试从缓存获取带规格的食品列表
	var cachedList *dto.TakeoutFoodResponseList
	var err error

	if categoryID > 0 {
		// 按商家和分类查询
		cachedList, err = s.cacheService.GetMerchantCategoryFoodListWithVariants(merchantID, categoryID, page, pageSize)
	} else if keyword != "" {
		// 按商家和关键词搜索
		cachedList, err = s.cacheService.GetSearchFoodListWithVariants(keyword, merchantID, page, pageSize)
	} else {
		// 按商家查询所有
		cachedList, err = s.cacheService.GetMerchantFoodListWithVariants(merchantID, page, pageSize)
	}

	// 如果缓存命中，需要过滤数据
	if err == nil && cachedList != nil {
		// 过滤出状态为上架销售中且审核通过的食品
		filteredList := make([]dto.TakeoutFoodListItemDTO, 0)
		for _, item := range cachedList.List {
			if item.Status == models.FoodStatusOnSale && item.AuditStatus == models.AuditStatusApproved {
				filteredList = append(filteredList, item)
			}
		}

		// 更新返回结果
		result := &dto.TakeoutFoodResponseList{
			Total: len(filteredList),
			List:  filteredList,
		}
		return result, nil
	}

	// 缓存未命中，构建查询参数
	query := &dto.TakeoutFoodQueryRequest{
		MerchantID:  merchantID,
		CategoryID:  categoryID,
		Keyword:     keyword,
		Page:        page,
		PageSize:    pageSize,
		Status:      models.FoodStatusOnSale,    // 默认查询上架销售中的商品
		AuditStatus: models.AuditStatusApproved, // 只查询审核通过的商品
	}

	foodListResp, err := s.ListFoods(query)
	if err != nil {
		return nil, err
	}

	// 批量查询所有食品ID的规格
	for i := range foodListResp.List {
		foodID := foodListResp.List[i].ID
		variants, err := s.variantRepo.ListByFoodID(foodID)
		if err != nil {
			// 日志警告但不中断
			logs.Warn("获取食品规格失败: %v, foodID=%d", err, foodID)
			continue
		}
		// 转换为DTO
		variantDTOs := make([]dto.TakeoutFoodVariantDTO, 0, len(variants))
		for _, v := range variants {
			variantDTOs = append(variantDTOs, dto.TakeoutFoodVariantDTO{
				ID:            v.ID,
				FoodID:        v.FoodID,
				Name:          v.Name,
				Description:   v.Description,
				Image:         v.Image,
				Price:         v.Price,
				OriginalPrice: v.OriginalPrice,
				Stock:         v.Stock,
				SoldCount:     v.SoldCount,
				IsDefault:     v.IsDefault,
				SortOrder:     v.SortOrder,
			})
		}
		foodListResp.List[i].Variants = variantDTOs
	}

	// 异步设置缓存
	go func() {
		if categoryID > 0 {
			// 按商家和分类缓存
			err := s.cacheService.SetMerchantCategoryFoodListWithVariants(merchantID, categoryID, page, pageSize, foodListResp)
			if err != nil {
				logs.Warn("设置商家分类食品列表(带规格)缓存失败: %v, 商家ID: %d, 分类ID: %d", err, merchantID, categoryID)
			}
		} else if keyword != "" {
			// 按商家和关键词搜索缓存
			err := s.cacheService.SetSearchFoodListWithVariants(keyword, merchantID, page, pageSize, foodListResp)
			if err != nil {
				logs.Warn("设置搜索食品列表(带规格)缓存失败: %v, 商家ID: %d, 关键词: %s", err, merchantID, keyword)
			}
		} else {
			// 按商家缓存所有
			err := s.cacheService.SetMerchantFoodListWithVariants(merchantID, page, pageSize, foodListResp)
			if err != nil {
				logs.Warn("设置商家食品列表(带规格)缓存失败: %v, 商家ID: %d", err, merchantID)
			}
		}
	}()

	return foodListResp, nil
}

// ListFoods 查询外卖食品列表
func (s *takeoutFoodService) ListFoods(query *dto.TakeoutFoodQueryRequest) (*dto.TakeoutFoodResponseList, error) {
	// 使用缓存验证服务来确保数据一致性
	// 这个方法会先尝试缓存，然后验证缓存数据的有效性
	// 如果缓存数据无效，会自动查询数据库并更新缓存
	cacheValidationService := NewTakeoutFoodCacheValidationService(s.foodRepo, s.categoryRepo, s.cacheService)
	return cacheValidationService.ValidateAndGetFoodList(query)
}

// UpdateFoodStatus 更新外卖食品状态
func (s *takeoutFoodService) UpdateFoodStatus(id int64, status int) error {
	// 检查状态值是否有效
	validStatus := map[int]bool{
		models.FoodStatusDraft:   true,
		models.FoodStatusOnSale:  true,
		models.FoodStatusOffSale: true,
		models.FoodStatusSoldOut: true,
	}

	if !validStatus[status] {
		return errors.New("无效的状态值")
	}

	// 获取食品信息用于缓存清理
	food, err := s.foodRepo.GetByID(id)
	if err != nil {
		logs.Error("获取食品信息失败: %v, ID: %d", err, id)
		return errors.New("获取食品信息失败")
	}

	// 更新状态
	err = s.foodRepo.UpdateStatus(id, status)
	if err != nil {
		logs.Error("更新外卖食品状态失败: %v, ID: %d, 状态: %d", err, id, status)
		return errors.New("更新状态失败")
	}

	// 同步清理缓存，确保状态更新和缓存清理的原子性
	// 删除单个食品缓存
	err = s.cacheService.DeleteFoodCache(id)
	if err != nil {
		logs.Warn("删除食品缓存失败: %v, 食品ID: %d", err, id)
	}
	// 清理商家相关缓存
	err = s.cacheService.ClearMerchantFoodCache(food.MerchantID)
	if err != nil {
		logs.Warn("清理商家食品缓存失败: %v, 商家ID: %d", err, food.MerchantID)
	}
	// 清理分类相关缓存
	if food.CategoryID > 0 {
		err = s.cacheService.ClearCategoryFoodCache(food.CategoryID)
		if err != nil {
			logs.Warn("清理分类食品缓存失败: %v, 分类ID: %d", err, food.CategoryID)
		}
	}
	logs.Debug("食品状态更新后缓存清理完成, 食品ID: %d", id)

	return nil
}

// UpdateSoldOutStatus 更新售罄状态
func (s *takeoutFoodService) UpdateSoldOutStatus(id int64, soldOut bool) error {
	// 获取食品信息用于缓存清理
	food, err := s.foodRepo.GetByID(id)
	if err != nil {
		logs.Error("获取食品信息失败: %v, ID: %d", err, id)
		return errors.New("获取食品信息失败")
	}

	// 更新售罄状态
	err = s.foodRepo.UpdateSoldOutStatus(id, soldOut)
	if err != nil {
		logs.Error("更新售罄状态失败: %v, ID: %d, 售罄: %v", err, id, soldOut)
		return errors.New("更新售罄状态失败")
	}

	// 如果设置为售罄，同时更新状态为已售罄
	if soldOut {
		err = s.foodRepo.UpdateStatus(id, models.FoodStatusSoldOut)
		if err != nil {
			logs.Warn("更新外卖食品状态为售罄失败: %v, ID: %d", err, id)
			// 不影响主流程
		}
	}

	// 同步清理缓存，确保状态更新和缓存清理的原子性
	// 删除单个食品缓存
	err = s.cacheService.DeleteFoodCache(id)
	if err != nil {
		logs.Warn("删除食品缓存失败: %v, 食品ID: %d", err, id)
	}
	// 清理商家相关缓存
	err = s.cacheService.ClearMerchantFoodCache(food.MerchantID)
	if err != nil {
		logs.Warn("清理商家食品缓存失败: %v, 商家ID: %d", err, food.MerchantID)
	}
	// 清理分类相关缓存
	if food.CategoryID > 0 {
		err = s.cacheService.ClearCategoryFoodCache(food.CategoryID)
		if err != nil {
			logs.Warn("清理分类食品缓存失败: %v, 分类ID: %d", err, food.CategoryID)
		}
	}
	logs.Debug("食品售罄状态更新后缓存清理完成, 食品ID: %d", id)

	return nil
}

// IncrementViewCount 增加浏览次数
func (s *takeoutFoodService) IncrementViewCount(id int64) error {
	return s.foodRepo.IncrementViewCount(id)
}

// GetFoodPriceRange 获取食品价格区间
func (s *takeoutFoodService) GetFoodPriceRange(foodID int64) (float64, float64, error) {
	// 获取变体列表
	variants, err := s.variantRepo.ListByFoodID(foodID)
	if err != nil {
		return 0, 0, err
	}

	if len(variants) == 0 {
		// 获取食品基本信息
		food, err := s.foodRepo.GetByID(foodID)
		if err != nil {
			return 0, 0, err
		}
		return food.Price, food.Price, nil
	}

	// 计算最低价和最高价
	var minPrice, maxPrice float64
	minPrice = variants[0].Price
	maxPrice = variants[0].Price

	for _, v := range variants {
		if v.Price < minPrice {
			minPrice = v.Price
		}
		if v.Price > maxPrice {
			maxPrice = v.Price
		}
	}

	return minPrice, maxPrice, nil
}

// AuditFoodForAdmin 管理员审核食品
func (s *takeoutFoodService) AuditFoodForAdmin(req *dto.AuditFoodRequest) error {
	// 获取食品信息
	food, err := s.foodRepo.GetByID(req.ID)
	if err != nil {
		logs.Error("获取食品信息失败: %v, ID: %d", err, req.ID)
		return errors.New("获取食品信息失败")
	}

	// 检查食品审核状态是否为待审核状态
	if food.AuditStatus != models.AuditStatusPending {
		return errors.New("该食品不在待审核状态，无法进行审核操作")
	}

	// 更新审核信息
	food.AuditorID = req.AuditorID
	food.AuditStatus = req.AuditStatus
	food.AuditReason = req.AuditReason
	food.AuditTime = time.Now() // 设置审核时间为当前时间

	// 根据审核结果更新食品状态
	if req.AuditStatus == models.AuditStatusApproved { // 审核通过
		// 只更新审核状态，商品状态保持当前状态（可能是草稿或其他状态）
		// 如果商家想上架商品，需要单独设置Status为上架状态
	} else if req.AuditStatus == models.AuditStatusRejected { // 审核拒绝
		// 审核拒绝，保持商品当前状态，只更新审核状态
	} else {
		return errors.New("无效的审核状态")
	}

	// 保存更新
	err = s.foodRepo.Update(food)
	if err != nil {
		logs.Error("更新食品审核信息失败: %v, 请求: %+v", err, req)
		return errors.New("更新食品审核信息失败")
	}

	return nil
}

// ClearMerchantFoodCache 清理商家所有食品相关缓存
func (s *takeoutFoodService) ClearMerchantFoodCache(merchantID int64) error {
	return s.cacheService.ClearMerchantFoodCache(merchantID)
}

// 实现ListMerchants方法
func (s *takeoutFoodService) ListMerchants(keyword string, page, pageSize int) ([]dto.MerchantListItemDTO, int64, error) {
	// 创建商家仓库实例
	merchantRepo := merchantRepositories.NewMerchantRepository()
	// 创建促销活动仓库实例
	promotionRepo := repositories.NewTakeoutPromotionRepository()
	ctx := context.Background()

	// 构建查询条件
	query := make(map[string]interface{})

	// 检查是否是按ID查询
	if len(keyword) > 3 && keyword[:3] == "id=" {
		// 从关键字中提取ID
		idStr := keyword[3:]
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err == nil && id > 0 {
			// 如果成功解析为ID，则使用ID查询
			query["id"] = id
			logs.Info("使用ID查询商家: %d", id)
		} else {
			// 解析失败，仍作为关键词处理
			query["keyword"] = keyword
		}
	} else if keyword != "" {
		// 正常的关键词查询
		query["keyword"] = keyword
	}

	// 设置状态为已审核通过且营业中的商家
	query["status"] = merchantModels.MerchantStatusApproved
	query["operation_status"] = merchantModels.MerchantOperationStatusOpen

	// 打印查询条件方便调试
	logs.Info("查询条件: %v, 页码: %d, 每页数量: %d", query, page, pageSize)

	// 获取商家列表
	merchants, total, err := merchantRepo.List(ctx, query, page, pageSize)
	if err != nil {
		logs.Error("获取商家列表失败: %v", err)
		return nil, 0, err
	}

	// 打印查询结果
	logs.Info("查询到商家数量: %d, 总记录数: %d", len(merchants), total)
	if len(merchants) > 0 {
		logs.Info("第一个商家ID: %d, 名称: %s", merchants[0].ID, merchants[0].Name)
	}

	// 转换为DTO
	merchantDTOs := make([]dto.MerchantListItemDTO, 0, len(merchants))
	for _, merchant := range merchants {
		// 获取商家的食品数量
		foodCount, err := s.foodRepo.CountByMerchantID(merchant.ID)
		if err != nil {
			logs.Warn("获取商家食品数量失败: %v, 商家ID: %d", err, merchant.ID)
			foodCount = 0
		}

		// 获取商家的活跃促销活动
		promotionInfo := "暂无促销活动"
		promotions := make([]dto.PromotionInfoDTO, 0)
		activePromotions, err := promotionRepo.GetActivePromotions(merchant.ID)
		logs.Info("商家ID: %d, 查询促销活动结果: err=%v, 活动数量=%d", merchant.ID, err, len(activePromotions))
		if err == nil && len(activePromotions) > 0 {
			logs.Info("商家ID: %d, 找到 %d 个活跃促销活动", merchant.ID, len(activePromotions))
			// 构建促销信息字符串和详细促销活动列表
			promotionTexts := make([]string, 0)
			for _, promotion := range activePromotions {
				var promotionText, typeName string
				switch promotion.Type {
				case models.PromotionTypeFirstOrder:
					promotionText = "首单优惠"
					typeName = "首单优惠"
				case models.PromotionTypeProductDiscount:
					promotionText = "商品折扣"
					typeName = "商品折扣"
				case models.PromotionTypeCoupon:
					promotionText = "优惠券活动"
					typeName = "优惠券"
				case models.PromotionTypeFull:
					promotionText = "满减优惠"
					typeName = "满减活动"
				case models.PromotionTypeLimitedTime:
					promotionText = "限时特价"
					typeName = "限时特价"
				default:
					promotionText = promotion.Name
					typeName = "其他"
				}
				if promotion.Description != "" {
					promotionText += "(" + promotion.Description + ")"
				}
				promotionTexts = append(promotionTexts, promotionText)

				// 构建详细促销活动信息
				promotionDTO := dto.PromotionInfoDTO{
					ID:          promotion.ID,
					Name:        promotion.Name,
					Description: promotion.Description,
					Type:        promotion.Type,
					TypeName:    typeName,
					Rules:       promotion.Rules,
					StartTime:   promotion.StartTime.Format("2006-01-02 15:04:05"),
					EndTime:     promotion.EndTime.Format("2006-01-02 15:04:05"),
				}
				promotions = append(promotions, promotionDTO)
			}
			if len(promotionTexts) > 0 {
				promotionInfo = promotionTexts[0] // 只显示第一个促销活动
				if len(promotionTexts) > 1 {
					promotionInfo += "等" + strconv.Itoa(len(promotionTexts)) + "项优惠"
				}
			}
		}

		// 转换为DTO
		merchantDTO := dto.MerchantListItemDTO{
			ID:              merchant.ID,
			Name:            merchant.Name,
			Logo:            merchant.Logo,
			Description:     merchant.Description,
			CategoryID:      merchant.CategoryID,
			CategoryName:    "", // 可以通过额外查询获取分类名称
			Address:         merchant.Address,
			Longitude:       merchant.Longitude, // 添加经度字段
			Latitude:        merchant.Latitude,  // 添加纬度字段
			OperationStatus: merchant.OperationStatus,
			FoodCount:       foodCount,
			BusinessHours:   merchant.BusinessHours,
			// 设置默认值或模拟数据
			DeliveryFee:    5.0,
			MinOrderAmount: 20.0,
			DeliveryTime:   30,
			Rating:         4.5,
			MonthSales:     200,
			IsRecommend:    merchant.Status == merchantModels.MerchantStatusApproved,
			PromotionInfo:  promotionInfo,
			Promotions:     promotions,
			OpeningTime:    "09:00",
			ClosingTime:    "21:00",
			CreatedAt:      merchant.CreatedAt,
		}

		merchantDTOs = append(merchantDTOs, merchantDTO)
	}

	return merchantDTOs, total, nil
}

// GetMerchantDetail 获取商家详情信息
func (s *takeoutFoodService) GetMerchantDetail(merchantID int64) (*dto.MerchantDetailDTO, error) {
	// 参数校验
	if merchantID <= 0 {
		logs.Error("参数错误: 商家ID不能为空")
		return nil, result.ErrInvalidParams
	}

	// 创建商家仓库实例
	merchantRepo := merchantRepositories.NewMerchantRepository()
	// 创建促销活动仓库实例
	promotionRepo := repositories.NewTakeoutPromotionRepository()
	ctx := context.Background()

	// 获取商家信息
	merchant, err := merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		logs.Error("获取商家信息失败: %v, 商家ID: %d", err, merchantID)
		return nil, result.ErrNotFound.WithDetails("商家不存在")
	}

	// 检查商家状态
	if merchant.Status != merchantModels.MerchantStatusApproved {
		logs.Warn("商家未审核通过: 商家ID=%d, 状态=%d", merchantID, merchant.Status)
		return nil, result.ErrNotFound.WithDetails("商家未审核通过")
	}

	// 获取商家的食品数量
	foodCount, err := s.foodRepo.CountByMerchantID(merchant.ID)
	if err != nil {
		logs.Warn("获取商家食品数量失败: %v, 商家ID: %d", err, merchant.ID)
		foodCount = 0
	}

	// 获取商家的活跃促销活动
	promotionInfo := "暂无促销活动"
	promotions := make([]dto.PromotionInfoDTO, 0)
	activePromotions, err := promotionRepo.GetActivePromotions(merchant.ID)
	logs.Info("商家ID: %d, 查询促销活动结果: err=%v, 活动数量=%d", merchant.ID, err, len(activePromotions))
	if err == nil && len(activePromotions) > 0 {
		logs.Info("商家ID: %d, 找到 %d 个活跃促销活动", merchant.ID, len(activePromotions))
		// 构建促销信息字符串和详细促销活动列表
		promotionTexts := make([]string, 0)
		for _, promotion := range activePromotions {
			var promotionText, typeName string
			switch promotion.Type {
			case models.PromotionTypeFirstOrder:
				promotionText = "首单优惠"
				typeName = "首单优惠"
			case models.PromotionTypeProductDiscount:
				promotionText = "商品折扣"
				typeName = "商品折扣"
			case models.PromotionTypeCoupon:
				promotionText = "优惠券活动"
				typeName = "优惠券"
			case models.PromotionTypeFull:
				promotionText = "满减优惠"
				typeName = "满减活动"
			case models.PromotionTypeLimitedTime:
				promotionText = "限时特价"
				typeName = "限时特价"
			default:
				promotionText = promotion.Name
				typeName = "其他"
			}
			if promotion.Description != "" {
				promotionText += "(" + promotion.Description + ")"
			}
			promotionTexts = append(promotionTexts, promotionText)

			// 构建详细促销活动信息
			promotionDTO := dto.PromotionInfoDTO{
				ID:          promotion.ID,
				Name:        promotion.Name,
				Description: promotion.Description,
				Type:        promotion.Type,
				TypeName:    typeName,
				Rules:       promotion.Rules,
				StartTime:   promotion.StartTime.Format("2006-01-02 15:04:05"),
				EndTime:     promotion.EndTime.Format("2006-01-02 15:04:05"),
			}
			promotions = append(promotions, promotionDTO)
		}
		if len(promotionTexts) > 0 {
			promotionInfo = promotionTexts[0] // 只显示第一个促销活动
			if len(promotionTexts) > 1 {
				promotionInfo += "等" + strconv.Itoa(len(promotionTexts)) + "项优惠"
			}
		}
	}

	// 从营业时间中提取开始和结束时间
	openingTime := "09:00"
	closingTime := "22:00"
	if merchant.BusinessHours != "" {
		// 尝试解析营业时间
		businessHours, err := merchant.GetBusinessHours()
		if err == nil && len(businessHours) > 0 {
			// 取第一个营业时间段作为开始和结束时间
			openingTime = businessHours[0].StartTime
			closingTime = businessHours[0].EndTime
		}
	}

	// 构建商家详情DTO
	merchantDetail := &dto.MerchantDetailDTO{
		ID:              merchant.ID,
		Name:            merchant.Name,
		Logo:            merchant.Logo,
		Description:     merchant.Description,
		CategoryID:      merchant.CategoryID,
		CategoryName:    "", // 可以通过额外查询获取分类名称
		ContactName:     merchant.ContactName,
		ContactMobile:   merchant.ContactMobile,
		Address:         merchant.Address,
		Longitude:       merchant.Longitude,
		Latitude:        merchant.Latitude,
		OperationStatus: merchant.OperationStatus,
		FoodCount:       foodCount,
		BusinessHours:   merchant.BusinessHours,
		OpeningTime:     openingTime,
		ClosingTime:     closingTime,
		// 设置默认值或模拟数据
		DeliveryFee:        5.0,
		MinOrderAmount:     20.0,
		DeliveryTime:       30,
		Rating:             4.5,
		MonthSales:         200,
		IsRecommend:        merchant.Status == merchantModels.MerchantStatusApproved,
		PromotionInfo:      promotionInfo,
		Promotions:         promotions,
		Photos:             []string{}, // 商家照片列表，可以从其他地方获取
		LatestAnnouncement: "",         // 最新公告，可以从其他地方获取
		CreatedAt:          merchant.CreatedAt,
		UpdatedAt:          merchant.UpdatedAt,
	}

	logs.Info("成功获取商家详情: 商家ID=%d, 商家名称=%s", merchantID, merchant.Name)
	return merchantDetail, nil
}

// GetMerchantsPromotionsAndCoupons 获取用户在指定商家的促销和优惠券信息
func (s *takeoutFoodService) GetMerchantsPromotionsAndCoupons(userID int64, merchantIDs []int64) ([]dto.MerchantPromotionCouponInfo, error) {
	ctx := context.Background()
	result := make([]dto.MerchantPromotionCouponInfo, 0, len(merchantIDs))

	// 创建商家仓库实例
	merchantRepo := merchantRepositories.NewMerchantRepository()
	// 创建促销活动仓库实例
	promotionRepo := repositories.NewTakeoutPromotionRepository()
	// 创建用户优惠券仓库实例
	userCouponRepo := repositories.NewTakeoutUserCouponRepository()

	for _, merchantID := range merchantIDs {
		// 获取商家信息
		merchant, err := merchantRepo.GetByID(ctx, merchantID)
		if err != nil {
			logs.Warn("获取商家信息失败: %v, 商家ID: %d", err, merchantID)
			continue
		}

		// 检查商家状态
		if merchant.Status != merchantModels.MerchantStatusApproved || merchant.OperationStatus != merchantModels.MerchantOperationStatusOpen {
			logs.Info("商家未营业或未审核通过，跳过: 商家ID=%d, 状态=%d, 营业状态=%d", merchantID, merchant.Status, merchant.OperationStatus)
			continue
		}

		merchantInfo := dto.MerchantPromotionCouponInfo{
			MerchantID:   merchantID,
			MerchantName: merchant.Name,
			Promotions:   make([]dto.PromotionInfoDTO, 0),
			Coupons:      make([]dto.UserCouponResponse, 0),
			HasPromotion: false,
			HasCoupon:    false,
		}

		// 获取商家的活跃促销活动
		activePromotions, err := promotionRepo.GetActivePromotions(merchantID)
		if err == nil && len(activePromotions) > 0 {
			for _, promotion := range activePromotions {
				// 检查用户是否可以使用该促销活动（考虑使用次数限制）
				canUse, err := s.checkUserCanUsePromotion(userID, promotion)
				if err != nil {
					logs.Warn("检查用户促销使用权限失败: %v, 用户ID: %d, 促销ID: %d", err, userID, promotion.ID)
					continue
				}

				// 如果用户不能使用该促销活动，跳过
				if !canUse {
					logs.Info("用户不能使用促销活动 - 用户ID: %d, 促销ID: %d, 促销名称: %s", userID, promotion.ID, promotion.Name)
					continue
				}

				var typeName string
				switch promotion.Type {
				case models.PromotionTypeFirstOrder:
					typeName = "首单优惠"
				case models.PromotionTypeProductDiscount:
					typeName = "商品折扣"
				case models.PromotionTypeCoupon:
					typeName = "优惠券"
				case models.PromotionTypeFull:
					typeName = "满减活动"
				case models.PromotionTypeLimitedTime:
					typeName = "限时特价"
				default:
					typeName = "其他"
				}

				promotionDTO := dto.PromotionInfoDTO{
					ID:          promotion.ID,
					Name:        promotion.Name,
					Description: promotion.Description,
					Type:        promotion.Type,
					TypeName:    typeName,
					Rules:       promotion.Rules,
					StartTime:   promotion.StartTime.Format("2006-01-02 15:04:05"),
					EndTime:     promotion.EndTime.Format("2006-01-02 15:04:05"),
				}
				merchantInfo.Promotions = append(merchantInfo.Promotions, promotionDTO)
			}

			// 如果有可用的促销活动，设置HasPromotion为true
			if len(merchantInfo.Promotions) > 0 {
				merchantInfo.HasPromotion = true
			}
		}

		// 获取用户在该商家的可用优惠券实例
		userCoupons, coupons, _, err := userCouponRepo.GetUserCouponsWithDetail(userID, models.UserCouponStatusUnused, 1, 100) // 获取未使用状态的优惠券
		if err == nil && len(userCoupons) > 0 {
			// 将优惠券信息关联到用户优惠券
			couponMap := make(map[int64]*models.TakeoutCoupon)
			for _, coupon := range coupons {
				couponMap[coupon.ID] = coupon
			}

			// 当前时间，用于检查优惠券是否过期
			now := time.Now()

			// 筛选属于当前商家且未过期的用户优惠券
			for _, userCoupon := range userCoupons {
				coupon := couponMap[userCoupon.CouponID]
				if coupon != nil && coupon.MerchantID == merchantID {
					// 检查优惠券是否在有效期内
					if now.Before(coupon.StartTime) || now.After(coupon.EndTime) {
						logs.Debug("优惠券已过期，跳过 - 优惠券ID: %d, 结束时间: %v, 当前时间: %v",
							coupon.ID, coupon.EndTime, now)
						continue
					}

					// 检查优惠券状态是否有效
					if coupon.Status != models.CouponStatusUnused {
						logs.Debug("优惠券状态无效，跳过 - 优惠券ID: %d, 状态: %d", coupon.ID, coupon.Status)
						continue
					}

					merchantInfo.HasCoupon = true
					// 转换为UserCouponResponse格式
					couponResponse := dto.ConvertToUserCouponResponse(userCoupon, coupon)
					if couponResponse != nil {
						merchantInfo.Coupons = append(merchantInfo.Coupons, *couponResponse)
					}
				}
			}
		}

		result = append(result, merchantInfo)
	}

	return result, nil
}

// GetMerchantAvailableCoupons 获取商家可领取的优惠券和促销信息
func (s *takeoutFoodService) GetMerchantAvailableCoupons(merchantID int64) (*dto.MerchantAvailableCouponsInfo, error) {
	ctx := context.Background()

	// 创建商家仓库实例
	merchantRepo := merchantRepositories.NewMerchantRepository()
	// 创建促销活动仓库实例
	promotionRepo := repositories.NewTakeoutPromotionRepository()
	// 创建优惠券仓库实例
	couponRepo := repositories.NewTakeoutCouponRepository()

	// 获取商家信息
	merchant, err := merchantRepo.GetByID(ctx, merchantID)
	if err != nil {
		return nil, errors.New("商家不存在")
	}

	// 检查商家状态
	if merchant.Status != merchantModels.MerchantStatusApproved {
		return nil, errors.New("商家未审核通过")
	}

	result := &dto.MerchantAvailableCouponsInfo{
		MerchantID:   merchantID,
		MerchantName: merchant.Name,
		Promotions:   make([]dto.PromotionInfoDTO, 0),
		Coupons:      make([]dto.CouponResponse, 0),
		HasPromotion: false,
		HasCoupon:    false,
	}

	// 获取商家的活跃促销活动
	activePromotions, err := promotionRepo.GetActivePromotions(merchantID)
	if err == nil && len(activePromotions) > 0 {
		result.HasPromotion = true
		for _, promotion := range activePromotions {
			var typeName string
			switch promotion.Type {
			case models.PromotionTypeFirstOrder:
				typeName = "首单优惠"
			case models.PromotionTypeProductDiscount:
				typeName = "商品折扣"
			case models.PromotionTypeCoupon:
				typeName = "优惠券"
			case models.PromotionTypeFull:
				typeName = "满减活动"
			case models.PromotionTypeLimitedTime:
				typeName = "限时特价"
			default:
				typeName = "其他"
			}

			promotionDTO := dto.PromotionInfoDTO{
				ID:          promotion.ID,
				Name:        promotion.Name,
				Description: promotion.Description,
				Type:        promotion.Type,
				TypeName:    typeName,
				Rules:       promotion.Rules,
				StartTime:   promotion.StartTime.Format("2006-01-02 15:04:05"),
				EndTime:     promotion.EndTime.Format("2006-01-02 15:04:05"),
			}
			result.Promotions = append(result.Promotions, promotionDTO)
		}
	}

	// 获取商家的可领取优惠券模板
	validCoupons, err := couponRepo.GetValidCoupons(merchantID)
	if err == nil && len(validCoupons) > 0 {
		result.HasCoupon = true
		for _, coupon := range validCoupons {
			// 转换为CouponResponse格式
			couponResponse := dto.ConvertToCouponResponse(coupon)
			if couponResponse != nil {
				result.Coupons = append(result.Coupons, *couponResponse)
			}
		}
	}

	return result, nil
}

// checkUserCanUsePromotion 检查用户是否可以使用指定的促销活动
func (s *takeoutFoodService) checkUserCanUsePromotion(userID int64, promotion *models.TakeoutPromotion) (bool, error) {
	// 解析促销活动规则
	var rules map[string]interface{}
	err := json.Unmarshal([]byte(promotion.Rules), &rules)
	if err != nil {
		logs.Error("解析促销活动规则失败: %v, 促销ID: %d", err, promotion.ID)
		return false, err
	}

	// 检查用户使用次数限制
	var perUserLimit int64 = 0

	// 尝试从不同的规则结构中获取per_user_limit
	if coupon, ok := rules["coupon"].(map[string]interface{}); ok {
		if limit, exists := coupon["per_user_limit"]; exists {
			if limitFloat, ok := limit.(float64); ok {
				perUserLimit = int64(limitFloat)
			}
		}
	} else if limit, exists := rules["per_user_limit"]; exists {
		if limitFloat, ok := limit.(float64); ok {
			perUserLimit = int64(limitFloat)
		}
	}

	// 如果没有设置per_user_limit或为0，默认允许使用
	if perUserLimit <= 0 {
		return true, nil
	}

	// 使用促销服务检查用户使用次数
	canUse, err := s.promotionService.CheckUserPromotionUsage(userID, promotion.ID, perUserLimit)
	if err != nil {
		logs.Error("检查用户促销使用次数失败: %v, 用户ID: %d, 促销ID: %d", err, userID, promotion.ID)
		return false, err
	}

	return canUse, nil
}
